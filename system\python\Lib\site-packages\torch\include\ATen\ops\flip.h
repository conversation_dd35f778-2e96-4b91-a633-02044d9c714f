#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/flip_ops.h>

namespace at {


// aten::flip(Tensor self, int[] dims) -> Tensor
inline at::Tensor flip(const at::Tensor & self, at::IntArrayRef dims) {
    return at::_ops::flip::call(self, dims);
}

// aten::flip.out(Tensor self, int[] dims, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & flip_out(at::Tensor & out, const at::Tensor & self, at::IntArrayRef dims) {
    return at::_ops::flip_out::call(self, dims, out);
}

// aten::flip.out(Tensor self, int[] dims, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & flip_outf(const at::Tensor & self, at::IntArrayRef dims, at::Tensor & out) {
    return at::_ops::flip_out::call(self, dims, out);
}

}
