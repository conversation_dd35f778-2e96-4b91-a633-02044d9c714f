#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/complex_ops.h>

namespace at {


// aten::complex(Tensor real, Tensor imag) -> Tensor
inline at::Tensor complex(const at::Tensor & real, const at::Tensor & imag) {
    return at::_ops::complex::call(real, imag);
}

// aten::complex.out(Tensor real, Tensor imag, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & complex_out(at::Tensor & out, const at::Tensor & real, const at::Tensor & imag) {
    return at::_ops::complex_out::call(real, imag, out);
}

// aten::complex.out(Tensor real, Tensor imag, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & complex_outf(const at::Tensor & real, const at::Tensor & imag, at::Tensor & out) {
    return at::_ops::complex_out::call(real, imag, out);
}

}
