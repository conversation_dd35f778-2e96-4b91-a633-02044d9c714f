#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/broadcast_tensors_ops.h>

namespace at {


// aten::broadcast_tensors(Tensor[] tensors) -> Tensor[]
inline ::std::vector<at::Tensor> broadcast_tensors(at::TensorList tensors) {
    return at::_ops::broadcast_tensors::call(tensors);
}

}
