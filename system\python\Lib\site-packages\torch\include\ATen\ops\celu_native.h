#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::Tensor celu(const at::Tensor & self, const at::Scalar & alpha=1.0);
TORCH_API at::Tensor & celu_out(const at::Tensor & self, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & celu_(at::Tensor & self, const at::Scalar & alpha=1.0);
} // namespace native
} // namespace at
